<template>
	<view class="detail-container">
		<!-- 顶部导航 -->
		<view class="header">
			<view class="nav-left" @click="goBack">
				<text class="nav-icon">←</text>
				<text class="nav-text">返回</text>
			</view>
			<view class="nav-title">收入详情</view>
			<view class="nav-right">
				<view class="action-btn save-btn" @click="saveRecord">
					<text class="btn-text">保存</text>
				</view>
			</view>
		</view>

		<!-- 详情表单 -->
		<view class="form-container">
			<!-- 日期工作汇总 -->
			<view class="section daily-summary">
				<view class="section-title">
					<view class="title-content">
						<text class="section-icon">📊</text>
						<text class="title-text">工作概况</text>
					</view>
					<view class="title-line"></view>
				</view>

				<!-- 日期汇总信息卡片 -->
				<view class="daily-summary-card">
					<!-- 日期和基本信息 -->
					<view class="summary-header">
						<view class="date-display">
							<text class="date-icon">📅</text>
							<text class="date-text">{{ selectedDate || '未设置日期' }}</text>
						</view>
						<view class="worker-count">
							<text class="count-icon">👥</text>
							<text class="count-text">{{ workRecords.length }}名工人</text>
						</view>
					</view>

					<!-- 工人列表 -->
					<view class="workers-list" v-if="workRecords.length > 0">
						<view class="workers-header">
							<text class="workers-title">参与工人</text>
							<view class="expand-btn" v-if="showExpandButton" @tap="toggleWorkersExpanded">
								<text class="expand-text">{{ expandButtonText }}</text>
							</view>
						</view>
						<view class="workers-tags">
							<view class="worker-tag" v-for="(workRecord, index) in displayedWorkers"
								:key="`worker-${index}-${workRecord.id || workRecord.worker_name}`">
								<text class="worker-name">{{ workRecord.worker_name }}</text>
								<text class="worker-production">{{ calculateWorkRecordProduction(workRecord).toFixed(1)
								}}斤</text>
							</view>
						</view>
					</view>

					<!-- 汇总数据 -->
					<view class="summary-stats">
						<!-- 第一行：上午产量、下午产量 -->
						<view class="stats-row">
							<view class="stat-item production-stat">
								<view class="stat-icon">🌅</view>
								<view class="stat-content">
									<text class="stat-label">上午产量</text>
									<text class="stat-value">{{ morningProduction.toFixed(2) }} 斤</text>
								</view>
							</view>

							<view class="stat-item production-stat">
								<view class="stat-icon">🌇</view>
								<view class="stat-content">
									<text class="stat-label">下午产量</text>
									<text class="stat-value">{{ afternoonProduction.toFixed(2) }} 斤</text>
								</view>
							</view>	
						</view>
						<!-- 第二行：日总产量、日总工钱 -->
						<view class="stats-row">
							<view class="stat-item production-stat">
								<view class="stat-icon">⚖️</view>
								<view class="stat-content">
									<text class="stat-label">日总产量</text>
									<text class="stat-value primary">{{ totalProduction.toFixed(2) }} 斤</text>
								</view>
							</view>
							<view class="stat-item cost-stat">
								<view class="stat-icon">💰</view>
								<view class="stat-content">
									<text class="stat-label">日总工钱</text>
									<text class="stat-value secondary">¥{{ dailyLaborCost.toFixed(2) }}</text>
								</view>
							</view>
						</view>
					</view>
				</view>
			</view>

			<!-- 销售记录输入 -->
			<view class="section sales-input">
				<view class="section-title">
					<view class="title-content">
						<text class="section-icon">💰</text>
						<text class="title-text">销售记录</text>
					</view>
					<view class="title-line"></view>
				</view>

				<!-- 销售模式选择 -->
				<view class="mode-selector">
					<view class="mode-options">
						<view class="mode-option"
							:class="{ active: salesMode === 'single_customer' }"
							@click="switchSalesMode('single_customer')">
							<view class="mode-header">
								<text class="mode-label">单客户模式</text>
								<view class="mode-indicator" v-if="salesMode === 'single_customer'">
									<text class="indicator-icon">✓</text>
								</view>
							</view>
						</view>

						<view class="mode-option"
							:class="{ active: salesMode === 'dual_customer' }"
							@click="switchSalesMode('dual_customer')">
							<view class="mode-header">
								<text class="mode-label">双客户模式</text>
								<view class="mode-indicator" v-if="salesMode === 'dual_customer'">
									<text class="indicator-icon">✓</text>
								</view>
							</view>
						</view>
					</view>
				</view>
			</view>

			<!-- 收益汇总 -->
			<view class="section profit-summary">
				<view class="section-title">
					<view class="title-content">
						<text class="section-icon">📈</text>
						<text class="title-text">收益汇总</text>
					</view>
					<view class="title-line"></view>
				</view>

				<view class="summary-cards">
					<view class="summary-card income-card">
						<view class="card-header">
							<text class="card-icon">💵</text>
							<text class="card-title">总收入</text>
						</view>
						<view class="card-value">
							<text class="value-amount income-amount">¥{{ totalIncome.toFixed(2) }}</text>
						</view>
					</view>

					<view class="summary-card cost-card">
						<view class="card-header">
							<text class="card-icon">💸</text>
							<text class="card-title">总成本</text>
						</view>
						<view class="card-value">
							<text class="value-amount cost-amount">¥{{ totalCost.toFixed(2) }}</text>
						</view>
					</view>

					<view class="summary-card profit-card" :class="profit >= 0 ? 'positive-profit' : 'negative-profit'">
						<view class="card-header">
							<text class="card-icon">{{ profit >= 0 ? '📊' : '📉' }}</text>
							<text class="card-title">毛利润</text>
						</view>
						<view class="card-value">
							<text class="value-amount profit-amount">{{ profit >= 0 ? '+' : '' }}¥{{ profit.toFixed(2)
							}}</text>
							<text class="profit-rate">{{ profitMargin.toFixed(1) }}%</text>
						</view>
					</view>
				</view>
			</view>

			<!-- 其他收入支出 -->
			<view class="section other-info">
				<view class="section-title">
					<view class="title-content">
						<text class="section-icon">📝</text>
						<text class="title-text">其他收入支出</text>
					</view>
					<view class="title-line"></view>
				</view>

				<view class="cost-grid">
					<view class="cost-item editable">
						<view class="cost-label">
							<text class="label-icon">💵</text>
							<text class="label">其他收入</text>
						</view>
						<view class="cost-value editable-value">
							<input class="value-input" type="number" v-model.number="otherIncome" placeholder="0.00"
								:min="0" :step="0.01" />
							<text class="currency">¥</text>
						</view>
					</view>

					<view class="cost-item editable">
						<view class="cost-label">
							<text class="label-icon">📝</text>
							<text class="label">其他支出</text>
						</view>
						<view class="cost-value editable-value">
							<input class="value-input" type="number" v-model.number="otherCost" placeholder="0.00"
								:min="0" :step="0.01" />
							<text class="currency">¥</text>
						</view>
					</view>
				</view>
			</view>
		</view>
	</view>
</template>



<style scoped>
.detail-container {
	min-height: 100vh;
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	position: relative;
	/* 适配状态栏高度 - 多种兼容方案 */
	padding-top: constant(safe-area-inset-top);
	/* iOS 11.0-11.2 */
	padding-top: env(safe-area-inset-top);
	/* iOS 11.2+ */
	/* 备用方案 */
	padding-top: var(--status-bar-height, 0px);
}

.detail-container::before {
	content: '';
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);
	pointer-events: none;
}

/* 顶部导航栏样式 - 参考detail-hourly.vue的稳定布局 */
.header {
	background: linear-gradient(135deg, #667eea, #764ba2);
	backdrop-filter: blur(20rpx);
	padding: 20rpx 30rpx;
	display: flex;
	justify-content: space-between;
	align-items: center;
	box-shadow: 0 2rpx 16rpx rgba(0, 0, 0, 0.1);
	position: sticky;
	top: 0;
	z-index: 100;
	border-bottom: 1rpx solid rgba(255, 255, 255, 0.2);
	/* 确保导航栏不会被状态栏遮挡 - 多种兼容方案 */
	margin-top: calc(-1 * constant(safe-area-inset-top));
	/* iOS 11.0-11.2 */
	margin-top: calc(-1 * env(safe-area-inset-top));
	/* iOS 11.2+ */
	margin-top: calc(-1 * var(--status-bar-height, 0px));
	/* 备用方案 */

	padding-top: calc(20rpx + constant(safe-area-inset-top));
	/* iOS 11.0-11.2 */
	padding-top: calc(20rpx + env(safe-area-inset-top));
	/* iOS 11.2+ */
	padding-top: calc(20rpx + var(--status-bar-height, 0px));
	/* 备用方案 */
}

/* 导航栏左侧 - 参考detail-hourly.vue */
.nav-left {
	display: flex;
	align-items: center;
	gap: 10rpx;
	cursor: pointer;
}

.nav-left:active {
	opacity: 0.7;
}

.nav-icon {
	font-size: 36rpx;
	color: #ffffff;
	text-shadow: 0 1rpx 2rpx rgba(0, 0, 0, 0.1);
}

.nav-text {
	font-size: 32rpx;
	color: #ffffff;
	text-shadow: 0 1rpx 2rpx rgba(0, 0, 0, 0.1);
}

/* 导航栏标题 */
.nav-title {
	font-size: 33rpx;
	font-weight: 700;
	color: #f4ebf4;
	text-shadow: 0 1rpx 2rpx rgba(0, 0, 0, 0.1);
}

.subtitle {
	font-size: 24rpx;
	color: #666;
	margin-top: 5rpx;
	display: block;
}

/* 导航栏右侧 */
.nav-right {
	min-width: 100rpx;
	text-align: right;
}

.action-btn {
	display: inline-flex;
	align-items: center;
	justify-content: center;
	padding: 12rpx 24rpx;
	border-radius: 20rpx;
	transition: all 0.3s ease;
	text-align: center;
}

.cancel-btn {
	background: rgb(233, 64, 64);
	border: 2rpx solid rgba(108, 117, 125, 0.3);
}

.cancel-btn:active {
	background: rgba(108, 117, 125, 0.2);
	transform: scale(0.95);
}

.save-btn {
	background: linear-gradient(135deg, #52c41a, #73d13d);
	border: 2rpx solid transparent;
}

.save-btn:active {
	transform: scale(0.95);
	opacity: 0.9;
}

.cancel-btn .btn-text {
	font-size: 28rpx;
	color: #ffffff;
	font-weight: 500;
}

.save-btn .btn-text {
	font-size: 28rpx;
	color: white;
	font-weight: 500;
}





.date-text {
	font-size: 24rpx;
	color: #999;
	margin-top: 5rpx;
	display: block;
}

.form-container {
	padding: 40rpx 30rpx 20rpx;
	max-width: 750rpx;
	margin: 0 auto;
	position: relative;
	z-index: 1;
}

.section {
	background: rgba(255, 255, 255, 0.95);
	backdrop-filter: blur(10rpx);
	border-radius: 24rpx;
	padding: 35rpx 30rpx;
	margin-bottom: 25rpx;
	box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.12);
	border: 1rpx solid rgba(255, 255, 255, 0.2);
	transition: all 0.3s ease;
}

.section:hover {
	transform: translateY(-4rpx);
	box-shadow: 0 12rpx 40rpx rgba(0, 0, 0, 0.15);
}

.section-title {
	display: flex;
	align-items: center;
	margin-bottom: 25rpx;
	position: relative;
}

.title-content {
	display: flex;
	align-items: center;
}

.section-icon {
	font-size: 36rpx;
	margin-right: 15rpx;
	filter: drop-shadow(0 2rpx 4rpx rgba(0, 0, 0, 0.1));
}

.title-text {
	font-size: 32rpx;
	font-weight: 700;
	color: #333;
	letter-spacing: 1rpx;
}

.title-line {
	flex: 1;
	height: 3rpx;
	background: linear-gradient(90deg, #667eea, #764ba2);
	margin-left: 20rpx;
	border-radius: 2rpx;
	opacity: 0.6;
}

/* ==================== 销售模式选择器 ==================== */

.mode-selector {
	padding: 10rpx 0;
}

.mode-options {
	display: flex;
	gap: 20rpx;
}

.mode-option {
	flex: 1;
	background: rgba(255, 255, 255, 0.8);
	border: 2rpx solid #e8eaff;
	border-radius: 16rpx;
	padding: 24rpx 20rpx;
	transition: all 0.3s ease;
	cursor: pointer;
	position: relative;
	overflow: hidden;
}

.mode-option::before {
	content: '';
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background: linear-gradient(135deg, #667eea, #764ba2);
	opacity: 0;
	transition: opacity 0.3s ease;
	z-index: 0;
}

.mode-option.active::before {
	opacity: 0.1;
}

.mode-option.active {
	border-color: #667eea;
	background: rgba(102, 126, 234, 0.05);
	transform: translateY(-2rpx);
	box-shadow: 0 8rpx 24rpx rgba(102, 126, 234, 0.15);
}

.mode-header {
	display: flex;
	align-items: center;
	justify-content: space-between;
	margin-bottom: 8rpx;
	position: relative;
	z-index: 1;
}

.mode-label {
	font-size: 28rpx;
	font-weight: 600;
	color: #333;
	transition: color 0.3s ease;
}

.mode-option.active .mode-label {
	color: #667eea;
}

.mode-indicator {
	width: 32rpx;
	height: 32rpx;
	background: #667eea;
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
	animation: scaleIn 0.3s ease;
}

.indicator-icon {
	color: white;
	font-size: 20rpx;
	font-weight: bold;
}

.mode-option.active .mode-desc {
	color: #555;
}

@keyframes scaleIn {
	0% {
		transform: scale(0);
		opacity: 0;
	}

	100% {
		transform: scale(1);
		opacity: 1;
	}
}

/* 表单网格布局 */
.form-grid {
	display: flex;
	flex-direction: column;
	gap: 25rpx;
}

.form-row {
	display: flex;
	gap: 20rpx;
	align-items: flex-start;
}

.form-item {
	display: flex;
	flex-direction: column;
	gap: 15rpx;
	flex: 1;
}

.form-item.half-width {
	flex: 1;
	min-width: 0;
	/* 防止内容溢出 */
}

/* ==================== 增强表单设计 ==================== */

/* 增强表单容器 */
.enhanced-form-container {
	display: flex;
	flex-direction: column;
	gap: 30rpx;
	padding: 10rpx 0;
}

/* 只读信息区域 */
.readonly-info-section {
	display: flex;
	gap: 20rpx;
	margin-bottom: 10rpx;
}

.info-card {
	flex: 1;
	background: linear-gradient(135deg, #f8f9ff 0%, #f0f2ff 100%);
	border-radius: 16rpx;
	padding: 24rpx 20rpx;
	border: 1rpx solid #e8eaff;
	box-shadow: 0 2rpx 8rpx rgba(99, 102, 241, 0.08);
	transition: all 0.3s ease;
}

.info-card:hover {
	transform: translateY(-2rpx);
	box-shadow: 0 4rpx 12rpx rgba(99, 102, 241, 0.12);
}

.info-item {
	display: flex;
	align-items: center;
	gap: 16rpx;
}

.info-icon {
	font-size: 32rpx;
	width: 48rpx;
	height: 48rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
	border-radius: 12rpx;
	box-shadow: 0 2rpx 6rpx rgba(99, 102, 241, 0.2);
}

.info-content {
	flex: 1;
	display: flex;
	flex-direction: column;
	gap: 4rpx;
}

.info-label {
	font-size: 24rpx;
	color: #6b7280;
	font-weight: 500;
}

.info-value {
	font-size: 28rpx;
	color: #1f2937;
	font-weight: 600;
}

/* 分隔线 */
.form-divider {
	height: 1rpx;
	background: linear-gradient(90deg, transparent 0%, #e5e7eb 50%, transparent 100%);
	margin: 20rpx 0;
}

/* 可编辑信息区域 */
.editable-info-section {
	display: flex;
	flex-direction: column;
	gap: 24rpx;
}

/* 主要字段容器 */
.primary-field-container {
	margin-bottom: 8rpx;
}

/* 次要字段容器 */
.secondary-fields-container {
	display: flex;
	flex-direction: column;
	gap: 20rpx;
}

/* 字段组通用样式 */
.field-group {
	display: flex;
	flex-direction: column;
	gap: 12rpx;
}

/* 字段头部 */
.field-header {
	display: flex;
	align-items: center;
	gap: 12rpx;
}

.field-icon {
	font-size: 28rpx;
	width: 40rpx;
	height: 40rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	border-radius: 10rpx;
	transition: all 0.3s ease;
}

.field-icon.primary {
	background: linear-gradient(135deg, #fbbf24 0%, #f59e0b 100%);
	box-shadow: 0 2rpx 6rpx rgba(251, 191, 36, 0.3);
}

.field-icon.secondary {
	background: linear-gradient(135deg, #9ca3af 0%, #6b7280 100%);
	box-shadow: 0 2rpx 6rpx rgba(156, 163, 175, 0.2);
}

.field-label {
	font-weight: 600;
	transition: color 0.3s ease;
}

.field-label.primary {
	font-size: 30rpx;
	color: #92400e;
}

.field-label.secondary {
	font-size: 28rpx;
	color: #374151;
}

.field-label.required::after {
	content: '*';
	color: #ef4444;
	margin-left: 6rpx;
	font-weight: bold;
	font-size: 32rpx;
}

.field-unit {
	font-size: 24rpx;
	color: #6b7280;
	font-weight: 500;
	margin-left: auto;
}

/* 增强输入框包装器 */
.enhanced-input-wrapper {
	position: relative;
	border-radius: 14rpx;
	transition: all 0.3s ease;
	overflow: hidden;
}

.enhanced-input-wrapper.primary {
	background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);
	border: 2rpx solid #f59e0b;
	box-shadow: 0 4rpx 12rpx rgba(245, 158, 11, 0.15);
}

.enhanced-input-wrapper.secondary {
	background: linear-gradient(135deg, #f9fafb 0%, #f3f4f6 100%);
	border: 2rpx solid #d1d5db;
	box-shadow: 0 2rpx 8rpx rgba(156, 163, 175, 0.1);
}

.enhanced-input-wrapper:focus-within {
	transform: translateY(-2rpx);
}

.enhanced-input-wrapper.primary:focus-within {
	border-color: #d97706;
	box-shadow: 0 6rpx 16rpx rgba(245, 158, 11, 0.25);
}

.enhanced-input-wrapper.secondary:focus-within {
	border-color: #9ca3af;
	box-shadow: 0 4rpx 12rpx rgba(156, 163, 175, 0.15);
}

/* 增强输入框 */
.enhanced-input {
	width: 100%;
	height: 88rpx;
	padding: 0 24rpx;
	border: none;
	background: transparent;
	font-size: 28rpx;
	font-weight: 500;
	transition: all 0.3s ease;
	outline: none;
}

.enhanced-input.primary {
	color: #92400e;
	font-weight: 600;
}

.enhanced-input.secondary {
	color: #374151;
}

.enhanced-input::placeholder {
	color: #9ca3af;
	font-weight: 400;
}

.enhanced-input.primary::placeholder {
	color: #d97706;
	opacity: 0.7;
}

/* 主要字段特殊样式 */
.primary-field {
	background: linear-gradient(135deg, #fffbeb 0%, #fef3c7 100%);
	border-radius: 16rpx;
	padding: 24rpx;
	border: 1rpx solid #fbbf24;
	box-shadow: 0 4rpx 12rpx rgba(251, 191, 36, 0.1);
	position: relative;
	overflow: hidden;
}

.primary-field::before {
	content: '';
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	height: 3rpx;
	background: linear-gradient(90deg, #fbbf24 0%, #f59e0b 100%);
}

/* 次要字段样式 */
.secondary-field {
	background: linear-gradient(135deg, #fafafa 0%, #f5f5f5 100%);
	border-radius: 14rpx;
	padding: 20rpx;
	border: 1rpx solid #e5e7eb;
	box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.04);
	transition: all 0.3s ease;
}

.secondary-field:hover {
	transform: translateY(-1rpx);
	box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.08);
}

/* 可编辑字段样式 */
.form-item .label.required::after {
	content: '*';
	color: #ff4757;
	margin-left: 5rpx;
	font-weight: bold;
}

.editable-wrapper {
	position: relative;
}

.editable-input {
	width: 100%;
	height: 80rpx;
	padding: 0 20rpx;
	border: 2rpx solid #e1e8ed;
	border-radius: 12rpx;
	font-size: 28rpx;
	background: white;
	transition: all 0.3s ease;
}

/* 紧凑型输入框 - 优化宽度 */
.compact-input {
	max-width: 280rpx;
	min-width: 200rpx;
}

.editable-input:focus {
	border-color: #667eea;
	box-shadow: 0 0 0 6rpx rgba(102, 126, 234, 0.1);
	outline: none;
}

/* 只读字段样式 */
.readonly-wrapper {
	background: linear-gradient(135deg, #f8f9fa, #e9ecef);
	border: 2rpx solid #dee2e6;
	border-radius: 12rpx;
	padding: 20rpx;
}

.readonly-text {
	font-size: 28rpx;
	color: #495057;
	font-weight: 500;
}

/* 收益汇总卡片样式 */
.summary-cards {
	display: flex;
	flex-direction: column;
	gap: 15rpx;
}

.summary-card {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 25rpx;
	border-radius: 16rpx;
	transition: all 0.3s ease;
	position: relative;
	overflow: hidden;
}

.summary-card::before {
	content: '';
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	opacity: 0.1;
	transition: opacity 0.3s ease;
}

.income-card {
	background: linear-gradient(135deg, #52c41a, #73d13d);
}

.cost-card {
	background: linear-gradient(135deg, #1890ff, #40a9ff);
}

.tea-picking-cost-card {
	background: linear-gradient(135deg, #52c41a, #73d13d);
	color: white;
}

.labor-cost-card {
	background: linear-gradient(135deg, #1890ff, #40a9ff);
	color: white;
}

.profit-card.positive-profit {
	background: linear-gradient(135deg, #f56a00, #fa8c16);
	color: white;
}

.profit-card.negative-profit {
	background: linear-gradient(135deg, #ff4d4f, #ff7875);
}

.card-header {
	display: flex;
	align-items: center;
	gap: 12rpx;
}

.card-icon {
	font-size: 32rpx;
}

.card-title {
	font-size: 28rpx;
	font-weight: 600;
}

.card-value {
	text-align: right;
}

.value-amount {
	font-size: 32rpx;
	font-weight: 700;
	display: block;
}

.tea-picking-cost-amount {
	color: white;
	text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

.labor-cost-amount {
	color: white;
	text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

.profit-rate {
	font-size: 24rpx;
	opacity: 0.9;
	margin-top: 5rpx;
	display: block;
}

.form-item.readonly {
	background: #f8f9fa;
	border-radius: 12rpx;
	padding: 15rpx;
}

/* 支出明细样式 */
.cost-grid {
	display: flex;
	flex-direction: column;
	gap: 25rpx;
}

.cost-item {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 25rpx;
	border-radius: 16rpx;
	transition: all 0.3s ease;
}

.cost-item.readonly {
	background: linear-gradient(135deg, #f8f9fa, #e9ecef);
	border: 2rpx solid #dee2e6;
}

.cost-item.editable {
	background: linear-gradient(135deg, #fff5f5, #ffe8e8);
	border: 2rpx solid #fbb6ce;
}

.cost-label {
	display: flex;
	align-items: center;
	gap: 12rpx;
}

.label-icon {
	font-size: 28rpx;
}

.cost-value {
	display: flex;
	align-items: center;
	gap: 10rpx;
}

.readonly-value .value-text {
	font-size: 28rpx;
	font-weight: 600;
	color: #495057;
}

.editable-value {
	position: relative;
}

.value-input {
	width: 200rpx;
	height: 60rpx;
	padding: 0 15rpx;
	border: 2rpx solid #e1e8ed;
	border-radius: 8rpx;
	font-size: 26rpx;
	text-align: right;
	background: white;
}

.value-input:focus {
	border-color: #667eea;
	outline: none;
}

.currency {
	font-size: 24rpx;
	color: #6c757d;
	font-weight: 500;
}

.label {
	font-size: 28rpx;
	color: #333;
	font-weight: 500;
}

.input-wrapper {
	flex: 1;
	position: relative;
}

.input {
	width: 100%;
	height: 70rpx;
	padding: 0 20rpx;
	border: 2rpx solid #e0e0e0;
	border-radius: 12rpx;
	font-size: 28rpx;
	background: white;
}

.input:focus {
	border-color: #2e7d32;
	outline: none;
}

.input-text {
	display: block;
	height: 70rpx;
	line-height: 70rpx;
	padding: 0 20rpx;
	border: 2rpx solid #e0e0e0;
	border-radius: 12rpx;
	font-size: 28rpx;
	background: white;
	color: #333;
}

.input-text.calculated {
	background: #f8f9fa;
	border-color: #f0f0f0;
	font-weight: 600;
}

.positive {
	color: #2e7d32;
}

.negative {
	color: #d32f2f;
}



/* 响应式设计 */
@media (max-width: 750rpx) {
	.header {
		padding: 20rpx;
		/* 移动端状态栏安全区域适配 - 多种兼容方案 */
		padding-top: calc(20rpx + constant(safe-area-inset-top));
		/* iOS 11.0-11.2 */
		padding-top: calc(20rpx + env(safe-area-inset-top));
		/* iOS 11.2+ */
		padding-top: calc(20rpx + var(--status-bar-height, 0px));
		/* 备用方案 */
	}

	/* 小屏幕导航栏适配 - 参考detail-hourly.vue */
	.nav-icon {
		font-size: 32rpx;
	}

	.nav-text {
		font-size: 28rpx;
	}

	.nav-title {
		font-size: 36rpx;
	}

	.action-btn {
		padding: 10rpx 20rpx;
		font-size: 26rpx;
	}

	.btn-text {
		font-size: 24rpx !important;
	}

	.form-container {
		padding: 30rpx 20rpx 20rpx;
	}

	.section {
		padding: 30rpx 25rpx;
		margin-bottom: 20rpx;
	}

	.form-grid {
		gap: 20rpx;
	}

	.form-row {
		flex-direction: column;
		gap: 15rpx;
	}

	.form-item.half-width {
		max-width: none;
	}

	.compact-input {
		max-width: none;
		min-width: auto;
	}

	.cost-item {
		flex-direction: column;
		align-items: stretch;
		gap: 12rpx;
	}

	.cost-value {
		justify-content: flex-end;
	}

	.value-input {
		width: 150rpx;
	}

	.summary-cards {
		gap: 12rpx;
	}

	.summary-card {
		padding: 20rpx;
	}

	.card-header {
		gap: 8rpx;
	}

	.card-icon {
		font-size: 26rpx;
	}

	.card-title {
		font-size: 24rpx;
	}

	.value-amount {
		font-size: 26rpx;
	}

	.profit-rate {
		font-size: 20rpx;
	}

	/* ==================== 增强表单响应式样式 ==================== */

	/* 只读信息区域响应式 */
	.readonly-info-section {
		flex-direction: column;
		gap: 16rpx;
	}

	.info-card {
		padding: 20rpx 16rpx;
		border-radius: 12rpx;
	}

	.info-icon {
		font-size: 28rpx;
		width: 40rpx;
		height: 40rpx;
		border-radius: 10rpx;
	}

	.info-label {
		font-size: 22rpx;
	}

	.info-value {
		font-size: 26rpx;
	}

	/* 可编辑区域响应式 */
	.editable-info-section {
		gap: 20rpx;
	}

	.primary-field {
		padding: 20rpx;
		border-radius: 14rpx;
	}

	.secondary-field {
		padding: 16rpx;
		border-radius: 12rpx;
	}

	.field-icon {
		font-size: 24rpx;
		width: 36rpx;
		height: 36rpx;
		border-radius: 8rpx;
	}

	.field-label.primary {
		font-size: 28rpx;
	}

	.field-label.secondary {
		font-size: 26rpx;
	}

	.field-unit {
		font-size: 22rpx;
	}

	.enhanced-input {
		height: 80rpx;
		padding: 0 20rpx;
		font-size: 26rpx;
	}

	.enhanced-input-wrapper {
		border-radius: 12rpx;
	}
}

/* 大屏幕优化 */
@media (min-width: 750rpx) {
	.readonly-info-section {
		gap: 24rpx;
	}

	.info-card {
		padding: 28rpx 24rpx;
	}

	.secondary-fields-container {
		display: flex;
		flex-direction: row;
		gap: 24rpx;
	}

	.secondary-field {
		flex: 1;
	}

	.enhanced-input-wrapper:hover {
		transform: translateY(-1rpx);
	}

	.enhanced-input-wrapper.primary:hover {
		box-shadow: 0 8rpx 20rpx rgba(245, 158, 11, 0.2);
	}

	.enhanced-input-wrapper.secondary:hover {
		box-shadow: 0 6rpx 16rpx rgba(156, 163, 175, 0.12);
	}
}

/* ==================== 工作记录信息样式 ==================== */
.work-record-info {
	display: flex;
	flex-direction: column;
	gap: 16rpx;
}

/* ==================== 销售记录列表样式 ==================== */
.sales-records {
	margin-top: 32rpx;
}

.sales-count {
	font-size: 22rpx;
	color: #666;
	margin-left: 8rpx;
}

.production-summary {
	background: #f8f9fa;
	padding: 20rpx;
	border-radius: 12rpx;
	margin-bottom: 24rpx;
}

.time-period-breakdown {
	margin-bottom: 16rpx;
	padding-bottom: 16rpx;
	border-bottom: 1rpx solid #e0e0e0;
}

.breakdown-title {
	margin-bottom: 12rpx;
}

.breakdown-label {
	font-size: 24rpx;
	color: #666;
	font-weight: 500;
}

.breakdown-items {
	display: flex;
	gap: 20rpx;
	flex-wrap: wrap;
}

.breakdown-item {
	display: flex;
	align-items: center;
	gap: 6rpx;
	padding: 6rpx 12rpx;
	background: white;
	border-radius: 8rpx;
	border: 1rpx solid #e0e0e0;
}

.period-label {
	font-size: 22rpx;
	color: #666;
}

.period-value {
	font-size: 22rpx;
	font-weight: 600;
	color: #3498db;
}

.allocation-status {
	display: flex;
	justify-content: space-between;
	align-items: center;
}

.summary-item {
	display: flex;
	align-items: center;
	gap: 8rpx;
}

.summary-label {
	font-size: 24rpx;
	color: #666;
}

.summary-value {
	font-size: 26rpx;
	font-weight: 600;
	color: #2c3e50;
}

.summary-value.warning {
	color: #e74c3c;
}

.summary-value.error {
	color: #e74c3c;
	font-weight: 700;
}

.summary-value.success {
	color: #27ae60;
}

.production-summary.error {
	border: 2rpx solid #e74c3c;
	background: #ffeaea;
}

.validation-message {
	display: flex;
	align-items: center;
	gap: 8rpx;
	margin-top: 12rpx;
	padding: 8rpx 12rpx;
	background: #fee;
	border-radius: 8rpx;
	border-left: 4rpx solid #e74c3c;
}

.error-icon {
	font-size: 20rpx;
}

.error-text {
	font-size: 22rpx;
	color: #e74c3c;
	font-weight: 500;
}

.field-input.error {
	border-color: #e74c3c;
	background: #ffeaea;
}

.error-tip {
	position: absolute;
	top: 100%;
	left: 0;
	font-size: 20rpx;
	color: #e74c3c;
	margin-top: 4rpx;
	padding: 4rpx 8rpx;
	background: #fee;
	border-radius: 4rpx;
	white-space: nowrap;
}

.sales-record-list {
	display: flex;
	flex-direction: column;
	gap: 24rpx;
}

/* 日期工作汇总样式 */
.daily-summary {
	margin-bottom: 32rpx;
}

.daily-summary-card {
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	border-radius: 20rpx;
	padding: 32rpx;
	color: white;
	box-shadow: 0 8rpx 24rpx rgba(102, 126, 234, 0.3);
}

.summary-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 24rpx;
}

.date-display {
	display: flex;
	align-items: center;
	gap: 12rpx;
}

.date-icon {
	font-size: 32rpx;
}

.date-text {
	font-size: 32rpx;
	font-weight: 600;
}

.worker-count {
	display: flex;
	align-items: center;
	gap: 8rpx;
	background: rgba(255, 255, 255, 0.2);
	padding: 8rpx 16rpx;
	border-radius: 20rpx;
}

.count-icon {
	font-size: 24rpx;
}

.count-text {
	font-size: 24rpx;
	font-weight: 500;
}

.workers-list {
	margin-bottom: 24rpx;
}

.workers-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 12rpx;
}

.expand-btn {
	background: rgba(255, 255, 255, 0.3);
	padding: 8rpx 16rpx;
	border-radius: 12rpx;
	cursor: pointer;
	transition: all 0.3s ease;
	/* 确保在移动端可点击 */
	min-width: 80rpx;
	min-height: 32rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	/* 防止文本选择 */
	user-select: none;
	-webkit-user-select: none;
	/* 触摸反馈 */
	-webkit-tap-highlight-color: rgba(255, 255, 255, 0.2);
}

.expand-btn:active {
	background: rgba(255, 255, 255, 0.5);
	transform: scale(0.95);
}

.expand-text {
	font-size: 20rpx;
	color: rgba(255, 255, 255, 0.9);
	font-weight: 500;
}

.workers-title {
	font-size: 24rpx;
	opacity: 0.9;
	font-weight: 500;
}

.workers-tags {
	display: flex;
	flex-wrap: wrap;
	gap: 12rpx;
}

.worker-tag {
	background: rgba(255, 255, 255, 0.2);
	padding: 8rpx 16rpx;
	border-radius: 16rpx;
	display: flex;
	align-items: center;
	gap: 8rpx;
}

.worker-name {
	font-size: 24rpx;
	font-weight: 500;
}

.worker-production {
	font-size: 22rpx;
	opacity: 0.8;
}

.summary-stats {
	display: flex;
	flex-direction: column;
	gap: 16rpx;
}

.stats-row {
	display: flex;
	justify-content: space-between;
	gap: 16rpx;
}

.stat-item {
	flex: 1;
	background: rgba(255, 255, 255, 0.15);
	padding: 20rpx;
	border-radius: 16rpx;
	display: flex;
	align-items: center;
	gap: 12rpx;
}

.stat-icon {
	font-size: 28rpx;
}

.stat-content {
	flex: 1;
}

.stat-label {
	font-size: 22rpx;
	opacity: 0.8;
	display: block;
	margin-bottom: 4rpx;
}

.stat-value {
	font-size: 26rpx;
	font-weight: 600;
	display: block;
}

.stat-value.primary {
	color: #fff;
	font-size: 28rpx;
}

.stat-value.secondary {
	color: #ffd700;
}

/* 销售记录管理样式优化 */
.sales-summary {
	display: flex;
	align-items: center;
	gap: 12rpx;
	margin-left: 12rpx;
}

.sales-count {
	background: #e3f2fd;
	color: #1976d2;
	padding: 6rpx 14rpx;
	border-radius: 12rpx;
	font-size: 26rpx;
	font-weight: 500;
}

.sales-allocation {
	background: #f3e5f5;
	color: #7b1fa2;
	padding: 6rpx 14rpx;
	border-radius: 12rpx;
	font-size: 26rpx;
	font-weight: 500;
}

/* 紧凑型销售记录列表 */
.compact-sales-list {
	display: flex;
	flex-direction: column;
	gap: 16rpx;
}

.compact-sales-item {
	background: white;
	border-radius: 12rpx;
	padding: 16rpx;
	box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.08);
	border: 2rpx solid transparent;
	transition: all 0.3s ease;
}



.item-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 12rpx;
}

.customer-badge {
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	color: white;
	width: 48rpx;
	height: 48rpx;
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
	font-size: 24rpx;
	font-weight: 600;
}

.customer-number {
	color: white;
	font-size: 26rpx;
}

.item-actions {
	display: flex;
	align-items: center;
}

.delete-btn {
	background: #ffebee;
	color: #f44336;
	width: 44rpx;
	height: 44rpx;
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
	font-size: 32rpx;
	font-weight: bold;
	transition: all 0.3s ease;
}

.delete-btn:active {
	background: #ffcdd2;
	transform: scale(0.9);
}

.delete-icon {
	color: #f44336;
}

/* 紧凑型输入区域 */
.compact-inputs {
	display: flex;
	flex-direction: column;
	gap: 12rpx;
}

.input-group {
	display: flex;
	align-items: center;
	gap: 12rpx;
}

.input-label {
	font-size: 28rpx;
	color: #666;
	min-width: 70rpx;
	font-weight: 500;
}

.input-container {
	flex: 1;
	display: flex;
	align-items: center;
	background: #f8f9fa;
	border-radius: 8rpx;
	padding: 0 12rpx;
	border: 2rpx solid transparent;
	transition: all 0.3s ease;
}

.input-container:focus-within {
	background: white;
	border-color: #667eea;
	box-shadow: 0 0 0 4rpx rgba(102, 126, 234, 0.1);
}

.compact-input {
	flex: 1;
	border: none;
	background: transparent;
	padding: 12rpx 0;
	font-size: 30rpx;
	color: #333;
}



.compact-input::placeholder {
	color: #bbb;
	font-size: 28rpx;
}

/* 只读输入框样式 */
.readonly-input {
	background: #f8f9fa;
	border: 1rpx solid #e9ecef;
	border-radius: 8rpx;
	padding: 12rpx 16rpx;
	display: flex;
	align-items: center;
	justify-content: center;
}

.readonly-value {
	font-size: 30rpx;
	color: #495057;
	font-weight: 500;
}

.input-unit {
	font-size: 26rpx;
	color: #999;
	margin-left: 8rpx;
	min-width: 45rpx;
}

.name-input {
	font-size: 28rpx;
}

/* 输入组特定样式 */
.production-group .input-container {
	background: #e8f5e8;
}

.production-group .input-container:focus-within {
	border-color: #4caf50;
	box-shadow: 0 0 0 4rpx rgba(76, 175, 80, 0.1);
}

.price-group .input-container {
	background: #fff3e0;
}

.price-group .input-container:focus-within {
	border-color: #ff9800;
	box-shadow: 0 0 0 4rpx rgba(255, 152, 0, 0.1);
}

.name-group .input-container {
	background: #f3e5f5;
}

.name-group .input-container:focus-within {
	border-color: #9c27b0;
	box-shadow: 0 0 0 4rpx rgba(156, 39, 176, 0.1);
}

/* 项目底部 */
.item-footer {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-top: 12rpx;
	padding-top: 12rpx;
	border-top: 1rpx solid #f0f0f0;
}

.subtotal-info {
	display: flex;
	align-items: center;
	gap: 12rpx;
}

.subtotal-amount {
	font-size: 32rpx;
	font-weight: 600;
	color: #4caf50;
}

.error-text {
	font-size: 24rpx;
	color: #f44336;
	background: #ffebee;
	padding: 4rpx 10rpx;
	border-radius: 8rpx;
}

/* 产量分配状态区域 */
.allocation-status-section {
	display: flex;
	flex-direction: column;
	gap: 12rpx;
	margin-top: 16rpx;
}



.allocation-status {
	text-align: center;
}

.status-text {
	font-size: 28rpx;
	color: #666;
	padding: 10rpx 18rpx;
	background: #f8f9fa;
	border-radius: 16rpx;
	display: inline-block;
}

.status-text.warning {
	color: #f44336;
	background: #ffebee;
}

.action-btn {
	padding: 8rpx 12rpx;
	border-radius: 8rpx;
	cursor: pointer;
}

.delete-btn {
	background: #fee;
	color: #e74c3c;
}

.btn-icon {
	font-size: 20rpx;
}

/* ==================== 销售记录输入样式 ==================== */

.sales-input {
	margin-bottom: 30rpx;
}

.mode-selector {
	margin-bottom: 30rpx;
}

.mode-options {
	display: flex;
	gap: 20rpx;
}

.mode-option {
	flex: 1;
	background: rgba(255, 255, 255, 0.8);
	border: 2rpx solid #e8eaff;
	border-radius: 16rpx;
	padding: 24rpx 20rpx;
	transition: all 0.3s ease;
	cursor: pointer;
	position: relative;
	overflow: hidden;
}

.mode-option::before {
	content: '';
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background: linear-gradient(135deg, #667eea, #764ba2);
	opacity: 0;
	transition: opacity 0.3s ease;
	z-index: 0;
}

.mode-option.active::before {
	opacity: 0.1;
}

.mode-option.active {
	border-color: #667eea;
	background: rgba(102, 126, 234, 0.05);
	transform: translateY(-2rpx);
	box-shadow: 0 8rpx 24rpx rgba(102, 126, 234, 0.15);
}

.mode-header {
	display: flex;
	align-items: center;
	justify-content: space-between;
	margin-bottom: 8rpx;
	position: relative;
	z-index: 1;
}

.mode-label {
	font-size: 28rpx;
	font-weight: 600;
	color: #333;
	transition: color 0.3s ease;
}

.mode-option.active .mode-label {
	color: #667eea;
}

.mode-indicator {
	width: 32rpx;
	height: 32rpx;
	background: #667eea;
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
	animation: scaleIn 0.3s ease;
}

.indicator-icon {
	color: white;
	font-size: 20rpx;
	font-weight: bold;
}

.mode-desc {
	font-size: 24rpx;
	color: #666;
	position: relative;
	z-index: 1;
	transition: color 0.3s ease;
}

.mode-option.active .mode-desc {
	color: #555;
}

@keyframes scaleIn {
	0% {
		transform: scale(0);
		opacity: 0;
	}
	100% {
		transform: scale(1);
		opacity: 1;
	}
}

.sales-form {
	display: flex;
	flex-direction: column;
	gap: 30rpx;
}

.customer-record {
	background: linear-gradient(135deg, #fffbeb 0%, #fef3c7 100%);
	border-radius: 16rpx;
	padding: 24rpx;
	border: 1rpx solid #fbbf24;
	box-shadow: 0 4rpx 12rpx rgba(251, 191, 36, 0.1);
	position: relative;
	overflow: hidden;
}

.customer-record::before {
	content: '';
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	height: 3rpx;
	background: linear-gradient(90deg, #fbbf24 0%, #f59e0b 100%);
}

.customer-header {
	margin-bottom: 20rpx;
	text-align: center;
}

.customer-title {
	font-size: 32rpx;
	font-weight: 700;
	color: #92400e;
	display: block;
}

.customer-subtitle {
	font-size: 24rpx;
	color: #d97706;
	margin-top: 4rpx;
	display: block;
}

.subtotal-display {
	background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
	border-radius: 12rpx;
	padding: 16rpx 20rpx;
	margin-top: 20rpx;
	display: flex;
	justify-content: space-between;
	align-items: center;
}

.subtotal-label {
	font-size: 28rpx;
	color: white;
	font-weight: 600;
}

.subtotal-value {
	font-size: 32rpx;
	color: white;
	font-weight: 700;
	text-shadow: 0 1rpx 2rpx rgba(0, 0, 0, 0.2);
}

.allocation-validation {
	margin-top: 20rpx;
}

.validation-warning {
	background: #fff3cd;
	border: 1rpx solid #ffeaa7;
	border-radius: 12rpx;
	padding: 16rpx 20rpx;
	display: flex;
	align-items: center;
	gap: 12rpx;
}

.warning-icon {
	font-size: 28rpx;
	color: #f39c12;
}

.warning-text {
	font-size: 26rpx;
	color: #856404;
	font-weight: 500;
}

.card-content {
	display: flex;
	flex-direction: column;
	gap: 20rpx;
}

.field-group {
	display: flex;
	flex-direction: column;
	gap: 8rpx;
}

.field-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
}

.field-label {
	font-size: 24rpx;
	color: #555;
	font-weight: 500;
}

.field-actions {
	display: flex;
	gap: 12rpx;
}

.auto-fill-btn {
	font-size: 22rpx;
	color: #3498db;
	padding: 4rpx 8rpx;
	border-radius: 4rpx;
	background: #e3f2fd;
	cursor: pointer;
}

.input-wrapper {
	position: relative;
}

.field-input {
	width: 100%;
	padding: 16rpx 20rpx;
	border: 2rpx solid #e0e0e0;
	border-radius: 12rpx;
	font-size: 26rpx;
	background: #fafafa;
	transition: all 0.3s ease;
}

.field-input:focus {
	border-color: #3498db;
	background: white;
	box-shadow: 0 0 0 4rpx rgba(52, 152, 219, 0.1);
}

.subtotal {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 12rpx 16rpx;
	background: #f0f8ff;
	border-radius: 8rpx;
	margin-top: 8rpx;
}

.subtotal-label {
	font-size: 24rpx;
	color: #666;
}

.subtotal-value {
	font-size: 26rpx;
	font-weight: 600;
	color: #3498db;
}

.add-sales-section {
	margin-top: 24rpx;
	text-align: center;
}

.add-sales-btn {
	display: inline-flex;
	align-items: center;
	gap: 8rpx;
	padding: 16rpx 32rpx;
	background: linear-gradient(135deg, #3498db, #2980b9);
	color: white;
	border-radius: 12rpx;
	font-size: 26rpx;
	font-weight: 500;
	cursor: pointer;
	transition: all 0.3s ease;
}

.add-sales-btn:active {
	transform: scale(0.98);
}

.add-sales-btn.disabled {
	background: #bdc3c7;
	cursor: not-allowed;
}



.add-tip {
	display: block;
	margin-top: 12rpx;
	font-size: 22rpx;
	color: #999;
}
</style>
