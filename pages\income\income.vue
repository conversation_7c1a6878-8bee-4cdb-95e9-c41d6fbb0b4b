<template>
	<view class="income-management-page">

		<!-- 时间范围选择 - 重新设计 -->
		<view class="enhanced-time-selector">
			<view class="time-selector-card">
				<view class="time-selector-header">
					<text class="time-icon">📅</text>
					<text class="time-title">时间范围</text>
				</view>

				<view class="enhanced-range-tabs">
					<view class="enhanced-range-tab" :class="{ active: timeRange === 'week' }"
						@click="setTimeRange('week')">
						<text class="tab-icon">📅</text>
						<text class="tab-text">本周</text>
					</view>
					<view class="enhanced-range-tab" :class="{ active: timeRange === 'month' }"
						@click="setTimeRange('month')">
						<text class="tab-icon">📆</text>
						<text class="tab-text">本月</text>
					</view>
					<view class="enhanced-range-tab" :class="{ active: timeRange === 'custom' }"
						@click="setTimeRange('custom')">
						<text class="tab-icon">🗓️</text>
						<text class="tab-text">自定义</text>
					</view>
				</view>

				<!-- 自定义时间范围 - 重新设计 -->
				<view class="enhanced-custom-range" v-if="timeRange === 'custom'">
					<view class="date-picker-container">
						<picker mode="date" :value="customStartDate" @change="onStartDateChange">
							<view class="enhanced-date-picker">
								<text class="date-icon">📅</text>
								<text class="date-text">{{ customStartDate || '开始日期' }}</text>
							</view>
						</picker>
						<view class="date-separator">
							<text class="separator-icon">→</text>
						</view>
						<picker mode="date" :value="customEndDate" @change="onEndDateChange">
							<view class="enhanced-date-picker">
								<text class="date-icon">📅</text>
								<text class="date-text">{{ customEndDate || '结束日期' }}</text>
							</view>
						</picker>
					</view>
				</view>
			</view>
		</view>

		<!-- 财务概览区域 -->
		<view class="financial-overview-section">
			<view class="overview-cards-container">
				<!-- 总收入卡片 -->
				<view class="overview-card income-card">
					<view class="card-icon-container">
						<text class="card-icon">💰</text>
					</view>
					<view class="card-content">
						<text class="card-title">总收入</text>
						<text class="card-value income-value">¥{{ summary.totalIncome.toFixed(2) }}</text>
					</view>
				</view>

				<!-- 总支出卡片 -->
				<view class="overview-card expense-card">
					<view class="card-icon-container">
						<text class="card-icon">💸</text>
					</view>
					<view class="card-content">
						<text class="card-title">总支出</text>
						<text class="card-value expense-value">¥{{ summary.totalCost.toFixed(2) }}</text>
					</view>
				</view>

				<!-- 毛利润卡片 -->
				<view class="overview-card profit-card" :class="{ 'profit-positive': summary.profit >= 0, 'profit-negative': summary.profit < 0 }">
					<view class="card-icon-container">
						<text class="card-icon">{{ summary.profit >= 0 ? '📈' : '📉' }}</text>
					</view>
					<view class="card-content">
						<text class="card-title">毛利润</text>
						<text class="card-value profit-value" :class="{ 'positive': summary.profit >= 0, 'negative': summary.profit < 0 }">
							{{ summary.profit >= 0 ? '+' : '' }}¥{{ summary.profit.toFixed(2) }}
						</text>
					</view>
				</view>
			</view>
		</view>

		<!-- 收入详情管理区域 -->
		<view class="income-details-management-section">
			<view class="income-section-header">
				<view class="section-title-group">
					<text class="section-icon">📋</text>
					<text class="section-title-text">收入详情</text>
				</view>
				<view class="records-count-badge" v-if="filteredData.length > 0">
					<text class="count-display-text">共 {{ filteredData.length }} 条记录</text>
				</view>
			</view>

			<!-- 数据加载状态显示 -->
			<view v-if="loading" class="data-loading-state">
				<view class="loading-status-card">
					<text class="loading-status-icon">⏳</text>
					<text class="loading-status-title">数据加载中</text>
					<text class="loading-status-description">正在获取收入记录...</text>
				</view>
			</view>

			<!-- 空数据状态显示 -->
			<view v-else-if="filteredData.length === 0" class="data-empty-state">
				<view class="empty-status-card">
					<text class="empty-status-icon">📋</text>
					<text class="empty-status-title">暂无收入数据</text>
					<text class="empty-status-description">当前时间段内没有收入记录</text>
				</view>
			</view>

			<!-- 收入记录列表容器 -->
			<view v-else class="income-records-container">
				<view class="income-record-item" :data-profit-status="getIncomeRecordProfitStatus(item)"
					v-for="(item, index) in filteredData" :key="index" @click="goToDetail(item)">
					<view class="record-display-card">
						<view class="record-card-header">
							<view class="record-date-display-section">
								<text class="date-display-icon">📅</text>
								<view class="date-display-info">
									<text class="date-display-text">{{ formatDateToShortDisplay(item.date) }}</text>
									<text class="weekday-display-text">{{ formatDateToWeekdayDisplay(item.date)
										}}</text>
								</view>
							</view>
							<view class="profit-status-indicator-badge" :class="getIncomeRecordProfitStatus(item)">
								<text class="status-indicator-icon">{{ getRecordStatusIcon(item) }}</text>
								<text class="status-indicator-text">{{ getRecordStatusText(item) }}</text>
							</view>
						</view>

						<view class="record-content-area">
							<!-- 基础业务信息展示 -->
							<view class="basic-business-info-summary">
								<view class="business-info-grid">
									<view class="business-info-card">
										<text class="business-info-icon">🍃</text>
										<view class="business-info-details">
											<text class="business-info-label">产量</text>
											<text class="business-info-value">{{ (item.production || 0).toFixed(2)
												}}斤</text>
										</view>
									</view>
									<view class="business-info-card">
										<text class="business-info-icon">💵</text>
										<view class="business-info-details">
											<text class="business-info-label">售卖单价</text>
											<text class="business-info-value">¥{{ getRecordAveragePriceDisplay(item)
												}}/斤</text>
										</view>
									</view>
								</view>
								<view class="customer-information-display" v-if="!item.isPlaceholder">
									<text class="customer-information-text" :class="getRecordProfitStyleClass(item)">
										客户: {{ getCompleteCustomerInfo(item) }}
									</text>
								</view>
							</view>

							<!-- 财务数据详情 -->
							<view class="financial-data-details-summary">
								<view class="financial-data-grid">
									<view class="primary-financial-card">
										<text class="primary-financial-icon">💰</text>
										<view class="primary-financial-info">
											<text class="primary-financial-label">总收入</text>
											<text class="primary-financial-value income">¥{{
												calculateRecordTotalIncome(item) }}</text>
										</view>
									</view>
									<view class="business-info-card">
										<text class="business-info-icon">💸</text>
										<view class="business-info-details">
											<text class="business-info-label">成本</text>
											<text class="business-info-value">¥{{
												calculateItemTotalCost(item).toFixed(2) }}</text>
										</view>
									</view>
								</view>
							</view>
						</view>

						<view class="record-summary-footer">
							<view class="profit-summary-display">
								<text class="profit-summary-label">毛利润</text>
								<text class="profit-summary-value" :class="getRecordProfitStyleClass(item)">¥{{
									calculateRecordProfit(item) }}</text>
							</view>
							<view class="detail-navigation-button">
								<text class="navigation-button-text">查看详情</text>
								<text class="navigation-button-icon">→</text>
							</view>
						</view>
					</view>
				</view>
			</view>
		</view>
	</view>
</template>


<style scoped>
.income-management-page {
	min-height: 100vh;
	background-color: #f5f7fa;
	color: #333;
	font-size: 28rpx;
	line-height: 1.5;
	padding-bottom: 40rpx;
}

.app-header {
	background: linear-gradient(135deg, #2e7d32, #2e7d32);
	color: white;
	padding: 40rpx 30rpx;
	border-radius: 0 0 40rpx 40rpx;
	box-shadow: 0 8rpx 24rpx rgba(46, 125, 50, 0.2);
	margin-bottom: 40rpx;
	position: relative;
	overflow: hidden;
}

.app-header::before {
	content: '';
	position: absolute;
	top: -100rpx;
	right: -100rpx;
	width: 400rpx;
	height: 400rpx;
	border-radius: 50%;
	background: rgba(255, 255, 255, 0.1);
}

.app-header::after {
	content: '';
	position: absolute;
	bottom: -60rpx;
	left: -60rpx;
	width: 200rpx;
	height: 200rpx;
	border-radius: 50%;
	background: rgba(255, 255, 255, 0.1);
}

.header-content {
	position: relative;
	z-index: 2;
	display: flex;
	justify-content: space-between;
	align-items: center;
}

.header-title {
	flex: 1;
}

.main-title {
	font-size: 44rpx;
	font-weight: 600;
	margin-bottom: 10rpx;
	display: block;
}

.sub-title {
	font-size: 28rpx;
	opacity: 0.9;
	display: block;
}

.user-avatar {
	width: 100rpx;
	height: 100rpx;
	border-radius: 50%;
	background: rgba(255, 255, 255, 0.2);
	display: flex;
	align-items: center;
	justify-content: center;
}

.avatar-icon {
	font-size: 48rpx;
}


.enhanced-time-selector {
	margin: 0 0 20rpx;
}

.time-selector-card {
	background: white;
	border-radius: 16rpx;
	padding: 30rpx;
	box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
	border: 1rpx solid rgba(255, 255, 255, 0.8);
}

.time-selector-header {
	display: flex;
	align-items: center;
	gap: 12rpx;
	margin-bottom: 25rpx;
}

.time-icon {
	font-size: 32rpx;
}

.time-title {
	font-size: 32rpx;
	font-weight: 600;
	color: #333;
}

.enhanced-range-tabs {
	display: flex;
	background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
	border-radius: 12rpx;
	padding: 8rpx;
	margin-bottom: 25rpx;
	gap: 6rpx;
}

.enhanced-range-tab {
	flex: 1;
	display: flex;
	flex-direction: column;
	align-items: center;
	gap: 6rpx;
	padding: 20rpx 10rpx;
	border-radius: 8rpx;
	cursor: pointer;
	transition: all 0.3s ease;
	background: transparent;
}

.enhanced-range-tab.active {
	background: linear-gradient(135deg, #4caf50 0%, #45a049 100%);
	color: white;
	transform: translateY(-2rpx);
	box-shadow: 0 4rpx 12rpx rgba(76, 175, 80, 0.3);
}

.tab-icon {
	font-size: 28rpx;
}

.tab-text {
	font-size: 28rpx;
	font-weight: 500;
}

.date-selector {
	background: white;
	border-radius: 12rpx;
	padding: 25rpx;
	box-shadow: 0 8rpx 20rpx rgba(0, 0, 0, 0.05);
	margin-bottom: 20rpx;
}

.date-title {
	margin-bottom: 15rpx;
}

.date-label {
	font-size: 32rpx;
	font-weight: 600;
	color: #333;
}

.date-controls {
	display: flex;
	align-items: center;
	gap: 20rpx;
	flex-wrap: wrap;
}

.date-input {
	padding: 20rpx 30rpx;
	border: 2rpx solid #e0e0e0;
	border-radius: 20rpx;
	font-size: 28rpx;
	flex: 1;
	min-width: 0;
	background: #f9f9f9;
	text-align: center;
}

.date-separator {
	font-size: 28rpx;
	color: #999;
}

.reset-button {
	align-self: center;
	background: #2e7d32;
	border-radius: 20rpx;
	padding: 15rpx 25rpx;
	min-width: 80rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	cursor: pointer;
	transition: all 0.3s ease;
}

.reset-button:hover {
	background: #1b5e20;
	transform: translateY(-2rpx);
}

.reset-text {
	font-size: 24rpx;
	color: white;
	font-weight: 500;
	text-align: center;
}

.refresh-button {
	align-self: center;
	background: #34c759;
	border-radius: 20rpx;
	padding: 15rpx 25rpx;
	min-width: 80rpx;
	margin-left: 10rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	cursor: pointer;
	transition: all 0.3s ease;
}

.refresh-button:hover {
	background: #30d158;
	transform: translateY(-2rpx);
}

/* 占位符文本样式 */
.placeholder-text {
	color: #999;
	font-style: italic;
}

.btn {
	padding: 20rpx 40rpx;
	border: none;
	border-radius: 20rpx;
	cursor: pointer;
	font-weight: 500;
	transition: all 0.3s ease;
	display: inline-flex;
	align-items: center;
	justify-content: center;
	gap: 16rpx;
	font-size: 28rpx;
}

.btn-primary {
	background: #2e7d32;
	color: white;
}

.btn-outline {
	background: transparent;
	border: 2rpx solid #2e7d32;
	color: #2e7d32;
}

.btn-sm {
	padding: 16rpx 24rpx;
}

.cards-container {
	display: grid;
	grid-template-columns: 1fr;
	gap: 20rpx;
	padding: 0 30rpx;
	margin-bottom: 30rpx;
}

.card {
	background: white;
	border-radius: 30rpx;
	padding: 30rpx;
	box-shadow: 0 8rpx 20rpx rgba(0, 0, 0, 0.05);
	border: 2rpx solid #f0f0f0;
	transition: all 0.3s ease;
}

.card:hover {
	transform: translateY(-4rpx);
	box-shadow: 0 12rpx 30rpx rgba(0, 0, 0, 0.1);
}

.profit-card {
	border-color: #2e7d32;
	background: linear-gradient(135deg, #f8fff8, #ffffff);
}

.card-header {
	display: flex;
	align-items: center;
	gap: 15rpx;
	margin-bottom: 20rpx;
	padding-bottom: 15rpx;
	border-bottom: 2rpx solid #f5f5f5;
}

.card-icon {
	font-size: 42rpx;
}

.card-title {
	font-size: 30rpx;
	font-weight: 600;
	color: #333;
}

.data-row {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 12rpx;
	padding: 10rpx 0;
}

.data-row:last-child {
	margin-bottom: 0;
}

.data-label {
	font-size: 26rpx;
	color: #666;
}

.data-value {
	font-size: 30rpx;
	font-weight: 600;
	color: #333;
}

.profit-value {
	font-size: 36rpx;
}

.positive {
	color: #2e7d32 !important;
	font-weight: 600 !important;
}

.negative {
	color: #d32f2f !important;
	font-weight: 600 !important;
}

/* 收入详情管理区域样式 - 现代化设计风格 */

/* 收入详情管理主容器 */
.income-details-management-section {
	margin: 0 20rpx 30rpx;
}

.income-section-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 20rpx;
	padding: 0 10rpx;
}

.section-title-group {
	display: flex;
	align-items: center;
	gap: 12rpx;
}

.section-icon {
	font-size: 32rpx;
}

.section-title-text {
	font-size: 40rpx;
	font-weight: 600;
	color: #333;
}

.records-count-badge {
	background: linear-gradient(135deg, #2e7d32 0%, #4caf50 100%);
	padding: 8rpx 16rpx;
	border-radius: 20rpx;
	box-shadow: 0 4rpx 12rpx rgba(46, 125, 50, 0.3);
}

.count-display-text {
	font-size: 30rpx;
	color: white;
	font-weight: 500;
}

/* 数据加载状态显示样式 */
.data-loading-state {
	margin: 60rpx 0;
}

.loading-status-card {
	background: white;
	border-radius: 20rpx;
	padding: 80rpx 40rpx;
	text-align: center;
	box-shadow: 0 12rpx 48rpx rgba(0, 0, 0, 0.1);
	border: 1rpx solid rgba(255, 255, 255, 0.8);
}

.loading-status-card .loading-status-icon {
	font-size: 120rpx;
	margin-bottom: 30rpx;
	opacity: 0.6;
}

.loading-status-card .loading-status-title {
	font-size: 36rpx;
	font-weight: 600;
	color: #333;
	margin-bottom: 20rpx;
}

.loading-status-card .loading-status-description {
	font-size: 28rpx;
	color: #666;
	line-height: 1.5;
}

/* 空数据状态显示样式 */
.data-empty-state {
	margin: 60rpx 0;
}

.empty-status-card {
	background: white;
	border-radius: 20rpx;
	padding: 80rpx 40rpx;
	text-align: center;
	box-shadow: 0 12rpx 48rpx rgba(0, 0, 0, 0.1);
	border: 1rpx solid rgba(255, 255, 255, 0.8);
}

.empty-status-card .empty-status-icon {
	font-size: 120rpx;
	margin-bottom: 30rpx;
	opacity: 0.6;
}

.empty-status-card .empty-status-title {
	font-size: 36rpx;
	font-weight: 600;
	color: #333;
	margin-bottom: 20rpx;
}

.empty-status-card .empty-status-description {
	font-size: 28rpx;
	color: #666;
	line-height: 1.5;
}

/* 收入记录列表容器 */
.income-records-container {
	display: flex;
	flex-direction: column;
	gap: 20rpx;
}

.income-record-item {
	cursor: pointer;
	transition: all 0.3s ease;
}

.income-record-item:active {
	transform: translateY(2rpx);
}

.record-display-card {
	background: white;
	border-radius: 20rpx;
	padding: 30rpx;
	box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
	border: 1rpx solid rgba(255, 255, 255, 0.8);
	transition: all 0.3s ease;
	overflow: hidden;
	position: relative;
}

.income-record-item:active .record-display-card {
	box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.15);
}

/* 不同盈利状态的记录卡片样式 */
.income-record-item[data-profit-status="profitable"] .record-display-card {
	background:
		linear-gradient(135deg, #ffffff 0%, rgba(46, 125, 50, 0.05) 100%),
		radial-gradient(circle at 20% 80%, rgba(46, 125, 50, 0.03) 0%, transparent 50%);
	border-left: 6rpx solid #2e7d32;
	box-shadow: 0 8rpx 32rpx rgba(46, 125, 50, 0.15);
}

.income-record-item[data-profit-status="profitable"] .record-display-card::before {
	content: '';
	position: absolute;
	top: 0;
	right: 0;
	width: 80rpx;
	height: 80rpx;
	background: linear-gradient(135deg, rgba(46, 125, 50, 0.1) 0%, rgba(46, 125, 50, 0.2) 100%);
	border-radius: 0 20rpx 0 80rpx;
}

.income-record-item[data-profit-status="loss"] .record-display-card {
	background:
		linear-gradient(135deg, #ffffff 0%, rgba(211, 47, 47, 0.05) 100%),
		radial-gradient(circle at 20% 80%, rgba(211, 47, 47, 0.03) 0%, transparent 50%);
	border-left: 6rpx solid #d32f2f;
	box-shadow: 0 8rpx 32rpx rgba(211, 47, 47, 0.15);
}

.income-record-item[data-profit-status="loss"] .record-display-card::before {
	content: '';
	position: absolute;
	top: 0;
	right: 0;
	width: 80rpx;
	height: 80rpx;
	background: linear-gradient(135deg, rgba(211, 47, 47, 0.1) 0%, rgba(211, 47, 47, 0.2) 100%);
	border-radius: 0 20rpx 0 80rpx;
}

.income-record-item[data-profit-status="placeholder"] .record-display-card {
	background:
		linear-gradient(135deg, #ffffff 0%, rgba(117, 117, 117, 0.05) 100%),
		radial-gradient(circle at 20% 80%, rgba(117, 117, 117, 0.03) 0%, transparent 50%);
	border-left: 6rpx solid #757575;
	box-shadow: 0 8rpx 32rpx rgba(117, 117, 117, 0.15);
}

.income-record-item[data-profit-status="placeholder"] .record-display-card::before {
	content: '';
	position: absolute;
	top: 0;
	right: 0;
	width: 80rpx;
	height: 80rpx;
	background: linear-gradient(135deg, rgba(117, 117, 117, 0.1) 0%, rgba(117, 117, 117, 0.2) 100%);
	border-radius: 0 20rpx 0 80rpx;
}

/* 记录卡片点击交互效果 */
.income-record-item[data-profit-status="profitable"]:active .record-display-card {
	background: linear-gradient(135deg, rgba(46, 125, 50, 0.08) 0%, rgba(46, 125, 50, 0.12) 100%);
	box-shadow: 0 4rpx 16rpx rgba(46, 125, 50, 0.25);
}

.income-record-item[data-profit-status="loss"]:active .record-display-card {
	background: linear-gradient(135deg, rgba(211, 47, 47, 0.08) 0%, rgba(211, 47, 47, 0.12) 100%);
	box-shadow: 0 4rpx 16rpx rgba(211, 47, 47, 0.25);
}

.income-record-item[data-profit-status="placeholder"]:active .record-display-card {
	background: linear-gradient(135deg, rgba(117, 117, 117, 0.08) 0%, rgba(117, 117, 117, 0.12) 100%);
	box-shadow: 0 4rpx 16rpx rgba(117, 117, 117, 0.25);
}

/* 记录卡片头部区域 */
.record-card-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 25rpx;
	position: relative;
	z-index: 1;
}

.record-date-display-section {
	display: flex;
	align-items: center;
	gap: 12rpx;
}

.date-display-icon {
	font-size: 28rpx;
	color: #666;
}

.date-display-info {
	display: flex;
	flex-direction: column;
	gap: 4rpx;
}

.date-display-text {
	font-size: 32rpx;
	font-weight: 600;
	color: #333;
	line-height: 1.2;
}

.weekday-display-text {
	font-size: 24rpx;
	color: #666;
	line-height: 1.2;
}

.profit-status-indicator-badge {
	display: flex;
	align-items: center;
	gap: 8rpx;
	padding: 12rpx 20rpx;
	border-radius: 30rpx;
	font-size: 26rpx;
	font-weight: 600;
	box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
	position: relative;
	z-index: 1;
}

.profit-status-indicator-badge.profitable {
	background: linear-gradient(135deg, #2e7d32 0%, #4caf50 100%);
	color: white;
	box-shadow: 0 4rpx 12rpx rgba(46, 125, 50, 0.3);
}

.profit-status-indicator-badge.loss {
	background: linear-gradient(135deg, #d32f2f 0%, #f44336 100%);
	color: white;
	box-shadow: 0 4rpx 12rpx rgba(211, 47, 47, 0.3);
}

.profit-status-indicator-badge.placeholder {
	background: linear-gradient(135deg, #757575 0%, #9e9e9e 100%);
	color: white;
	box-shadow: 0 4rpx 12rpx rgba(117, 117, 117, 0.3);
}

.profit-status-indicator-badge.neutral {
	background: linear-gradient(135deg, #616161 0%, #757575 100%);
	color: white;
	box-shadow: 0 4rpx 12rpx rgba(97, 97, 97, 0.3);
}

.status-indicator-icon {
	font-size: 24rpx;
}

.status-indicator-text {
	font-size: 26rpx;
	font-weight: 600;
}

/* 记录内容区域 */
.record-content-area {
	margin-bottom: 25rpx;
	position: relative;
	z-index: 1;
}

/* 基础业务信息摘要 */
.basic-business-info-summary {
	margin-bottom: 20rpx;
}

.business-info-grid {
	display: grid;
	grid-template-columns: 1fr 1fr;
	gap: 15rpx;
	margin-bottom: 15rpx;
}

.business-info-card {
	display: flex;
	align-items: center;
	gap: 12rpx;
	padding: 20rpx;
	border-radius: 12rpx;
	border: 1rpx solid rgba(0, 0, 0, 0.05);
	transition: all 0.3s ease;
	background: linear-gradient(135deg, rgba(248, 249, 250, 0.8) 0%, rgba(233, 236, 239, 0.8) 100%);
}

.primary-financial-card {
	display: flex;
	align-items: center;
	gap: 12rpx;
	padding: 20rpx;
	border-radius: 12rpx;
	border: 1rpx solid rgba(46, 125, 50, 0.2);
	transition: all 0.3s ease;
	background: linear-gradient(135deg, rgba(46, 125, 50, 0.08) 0%, rgba(46, 125, 50, 0.12) 100%);
}

.business-info-icon {
	font-size: 28rpx;
	color: #666;
	opacity: 0.8;
}

.business-info-details {
	display: flex;
	flex-direction: column;
	gap: 4rpx;
	flex: 1;
}

.business-info-label {
	font-size: 30rpx;
	color: #666;
	line-height: 1.2;
}

.business-info-value {
	font-size: 28rpx;
	color: #333;
	font-weight: 600;
	line-height: 1.2;
}

.primary-financial-icon {
	font-size: 28rpx;
	color: #2e7d32;
}

.primary-financial-info {
	display: flex;
	flex-direction: column;
	gap: 4rpx;
	flex: 1;
}

.primary-financial-label {
	font-size: 30rpx;
	color: #666;
	line-height: 1.2;
}

.primary-financial-value {
	font-size: 28rpx;
	font-weight: 600;
	line-height: 1.2;
}

.primary-financial-value.income {
	color: #2e7d32;
}

.customer-information-display {
	text-align: center;
	padding: 8rpx 16rpx;
	background: linear-gradient(135deg, rgba(248, 249, 250, 0.9) 0%, rgba(233, 236, 239, 0.9) 100%);
	border-radius: 20rpx;
	border: 1rpx solid rgba(0, 0, 0, 0.05);
}

.customer-information-text {
	font-size: 30rpx;
	font-weight: 500;
}

.customer-information-text.positive {
	color: #2e7d32;
}

.customer-information-text.negative {
	color: #d32f2f;
}

.customer-information-text.neutral {
	color: #666;
}

.customer-information-text.placeholder {
	color: #999;
}

/* 财务数据详情摘要 */
.financial-data-details-summary {
	margin-bottom: 20rpx;
}

.financial-data-grid {
	display: grid;
	grid-template-columns: 1fr 1fr;
	gap: 15rpx;
}

/* 记录摘要底部区域 */
.record-summary-footer {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 20rpx 25rpx;
	background: linear-gradient(135deg, rgba(248, 249, 250, 0.9) 0%, rgba(233, 236, 239, 0.9) 100%);
	border-radius: 16rpx;
	border: 1rpx solid rgba(0, 0, 0, 0.05);
	position: relative;
	z-index: 1;
}

.profit-summary-display {
	display: flex;
	align-items: center;
	gap: 12rpx;
}

.profit-summary-label {
	font-size: 30rpx;
	color: #666;
	font-weight: 500;
}

.profit-summary-value {
	font-size: 36rpx;
	font-weight: 700;
	text-shadow: 0 1rpx 2rpx rgba(0, 0, 0, 0.1);
}

.profit-summary-value.positive {
	color: #2e7d32;
}

.profit-summary-value.negative {
	color: #d32f2f;
}

.profit-summary-value.neutral {
	color: #666;
}

.profit-summary-value.placeholder {
	color: #999;
}

.detail-navigation-button {
	display: flex;
	align-items: center;
	gap: 8rpx;
	padding: 12rpx 20rpx;
	background: linear-gradient(135deg, #2e7d32 0%, #4caf50 100%);
	border-radius: 25rpx;
	box-shadow: 0 4rpx 12rpx rgba(46, 125, 50, 0.3);
}

.navigation-button-text {
	font-size: 24rpx;
	color: white;
	font-weight: 500;
}

.navigation-button-icon {
	font-size: 20rpx;
	color: white;
	font-weight: bold;
}

/* 时间选择器样式 */
.enhanced-time-selector {
	margin: 20rpx 30rpx;
	margin-bottom: 20rpx;
}

.time-selector-card {
	background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
	border-radius: 16rpx;
	padding: 30rpx;
	box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.08);
	border: 1rpx solid rgba(255, 255, 255, 0.8);
	position: relative;
	overflow: hidden;
}

.time-selector-card::before {
	content: '';
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	height: 4rpx;
	background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
}

.time-selector-header {
	display: flex;
	align-items: center;
	gap: 12rpx;
	margin-bottom: 25rpx;
}

.time-icon {
	font-size: 32rpx;
}

.time-title {
	font-size: 32rpx;
	font-weight: 600;
	color: #333;
}

.enhanced-range-tabs {
	display: flex;
	background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
	border-radius: 12rpx;
	padding: 8rpx;
	margin-bottom: 25rpx;
	gap: 6rpx;
}

.enhanced-range-tab {
	flex: 1;
	display: flex;
	flex-direction: column;
	align-items: center;
	gap: 6rpx;
	padding: 20rpx 10rpx;
	border-radius: 8rpx;
	cursor: pointer;
	transition: all 0.3s ease;
	background: transparent;
}

.enhanced-range-tab.active {
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	color: white;
	transform: translateY(-2rpx);
	box-shadow: 0 4rpx 12rpx rgba(102, 126, 234, 0.3);
}

.tab-icon {
	font-size: 28rpx;
}

.tab-text {
	font-size: 28rpx;
	font-weight: 500;
}

.enhanced-range-tab:not(.active) .tab-icon,
.enhanced-range-tab:not(.active) .tab-text {
	color: #666;
}

.enhanced-custom-range {
	margin-top: 25rpx;
}

.date-picker-container {
	display: flex;
	align-items: center;
	gap: 20rpx;
}

.enhanced-date-picker {
	flex: 1;
	display: flex;
	align-items: center;
	gap: 12rpx;
	padding: 24rpx;
	background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
	border-radius: 12rpx;
	border: 2rpx solid transparent;
	transition: all 0.3s ease;
}

.enhanced-date-picker:active {
	background: linear-gradient(135deg, #e9ecef 0%, #dee2e6 100%);
	transform: scale(0.98);
}

.date-icon {
	font-size: 28rpx;
	color: #666;
}

.date-text {
	font-size: 30rpx;
	color: #333;
	font-weight: 500;
}

.date-separator {
	display: flex;
	align-items: center;
	justify-content: center;
	width: 60rpx;
	height: 60rpx;
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	border-radius: 50%;
	box-shadow: 0 4rpx 12rpx rgba(102, 126, 234, 0.3);
}

.separator-icon {
	font-size: 24rpx;
	color: white;
	font-weight: bold;
}

/* 财务概览区域样式 */
.financial-overview-section {
	margin: 20rpx 30rpx;
	margin-bottom: 30rpx;
}

.overview-cards-container {
	display: flex;
	gap: 20rpx;
	justify-content: space-between;
}

.overview-card {
	flex: 1;
	background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
	border-radius: 16rpx;
	padding: 24rpx 20rpx;
	box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.08);
	border: 1rpx solid rgba(255, 255, 255, 0.2);
	display: flex;
	flex-direction: column;
	align-items: center;
	text-align: center;
	transition: all 0.3s ease;
	position: relative;
	overflow: hidden;
}

.overview-card::before {
	content: '';
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	height: 4rpx;
	background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
}

.overview-card:active {
	transform: translateY(2rpx);
	box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.12);
}

/* 总收入卡片样式 */
.income-card {
	background: linear-gradient(135deg, #e8f5e8 0%, #c8e6c9 100%);
}

.income-card::before {
	background: linear-gradient(90deg, #4caf50 0%, #2e7d32 100%);
}

/* 总支出卡片样式 */
.expense-card {
	background: linear-gradient(135deg, #fff3e0 0%, #ffe0b2 100%);
}

.expense-card::before {
	background: linear-gradient(90deg, #ff9800 0%, #f57c00 100%);
}

/* 毛利润卡片样式 */
.profit-card {
	background: linear-gradient(135deg, #f3e5f5 0%, #e1bee7 100%);
}

.profit-card.profit-positive {
	background: linear-gradient(135deg, #e8f5e8 0%, #c8e6c9 100%);
}

.profit-card.profit-positive::before {
	background: linear-gradient(90deg, #4caf50 0%, #2e7d32 100%);
}

.profit-card.profit-negative {
	background: linear-gradient(135deg, #ffebee 0%, #ffcdd2 100%);
}

.profit-card.profit-negative::before {
	background: linear-gradient(90deg, #f44336 0%, #d32f2f 100%);
}

.card-icon-container {
	margin-bottom: 12rpx;
}

.card-icon {
	font-size: 48rpx;
	line-height: 1;
}

.card-content {
	display: flex;
	flex-direction: column;
	align-items: center;
	gap: 8rpx;
}

.card-title {
	font-size: 24rpx;
	color: #666;
	font-weight: 500;
	line-height: 1.2;
}

.card-value {
	font-size: 32rpx;
	font-weight: 600;
	line-height: 1.2;
}

.income-value {
	color: #2e7d32;
}

.expense-value {
	color: #f57c00;
}

.profit-value.positive {
	color: #2e7d32;
}

.profit-value.negative {
	color: #d32f2f;
}

/* 响应式设计 */
@media (max-width: 750rpx) {
	.enhanced-time-selector {
		margin: 15rpx 20rpx;
		margin-bottom: 15rpx;
	}

	.time-selector-card {
		padding: 25rpx;
	}

	.time-icon {
		font-size: 28rpx;
	}

	.time-title {
		font-size: 28rpx;
	}

	.tab-icon {
		font-size: 24rpx;
	}

	.tab-text {
		font-size: 24rpx;
	}

	.enhanced-range-tab {
		padding: 16rpx 8rpx;
	}

	.date-picker-container {
		flex-direction: column;
		gap: 15rpx;
	}

	.date-separator {
		transform: rotate(90deg);
		width: 50rpx;
		height: 50rpx;
	}

	.separator-icon {
		font-size: 20rpx;
	}

	.enhanced-date-picker {
		padding: 20rpx;
	}

	.date-icon {
		font-size: 24rpx;
	}

	.date-text {
		font-size: 26rpx;
	}

	.financial-overview-section {
		margin: 15rpx 20rpx;
		margin-bottom: 25rpx;
	}

	.overview-cards-container {
		gap: 15rpx;
	}

	.overview-card {
		padding: 20rpx 16rpx;
	}

	.card-icon {
		font-size: 40rpx;
	}

	.card-title {
		font-size: 22rpx;
	}

	.card-value {
		font-size: 28rpx;
	}

	.enhanced-income-section {
		margin-left: 15rpx;
		margin-right: 15rpx;
	}

	.income-card {
		padding: 25rpx;
	}

	.financial-grid,
	.details-grid {
		grid-template-columns: 1fr;
		gap: 12rpx;
	}

	.enhanced-income-footer {
		flex-direction: column;
		gap: 15rpx;
		align-items: stretch;
	}

	.view-detail-btn {
		justify-content: center;
	}

	.profit-status-badge {
		padding: 10rpx 16rpx;
		font-size: 24rpx;
	}

	.date-text {
		font-size: 30rpx;
	}

	.weekday-text {
		font-size: 22rpx;
	}

	.financial-value {
		font-size: 26rpx;
	}

	.summary-value {
		font-size: 32rpx;
	}
}

/* 大屏设备优化 */
@media (min-width: 1200rpx) {
	.enhanced-income-list {
		display: grid;
		grid-template-columns: 1fr 1fr;
		gap: 20rpx;
	}

	.financial-grid,
	.details-grid {
		grid-template-columns: 1fr 1fr;
	}
}
</style>
